/* Dobrodosli.de - Styles */
/* Base styles consistent with Balkan Recruiters */

[x-cloak] { 
    display: none !important;
}

/* Base styles */
body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
}

:root {
    --primary-color: #023679;
    --primary-light: #1e40af;
    --primary-dark: #1e3a8a;
    --accent-color: #f59e0b;
    --text-dark: #1f2937;
    --text-light: #6b7280;
    --bg-light: #f9fafb;
    --border-color: #e5e7eb;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    color: var(--text-dark);
    font-weight: 600;
}

.text-primary {
    color: var(--primary-color);
}

.bg-primary {
    background-color: var(--primary-color);
}

.border-primary {
    border-color: var(--primary-color);
}

/* Navigation styles */
.nav-link {
    position: relative;
    font-weight: 500;
    transition: color 0.3s ease;
    color: var(--text-dark);
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 2px;
    bottom: -4px;
    left: 0;
    background-color: var(--primary-color);
    transform: scaleX(0);
    transform-origin: bottom right;
    transition: transform 0.3s ease;
}

.nav-link:hover::after {
    transform-origin: bottom left;
    transform: scaleX(1);
}

/* Hero section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-content {
    animation: fadeInUp 0.8s ease forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Section styles */
.section {
    padding: 4rem 0;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 1rem;
    text-align: center;
}

.section-subtitle {
    font-size: 1.125rem;
    color: var(--text-light);
    text-align: center;
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Card styles */
.info-card {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid var(--border-color);
}

.info-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
}

/* Accordion styles */
.accordion-item {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    margin-bottom: 1rem;
    overflow: hidden;
    background: white;
}

.accordion-header {
    background: var(--bg-light);
    padding: 1.5rem;
    cursor: pointer;
    transition: background-color 0.3s ease;
    border: none;
    width: 100%;
    text-align: left;
    display: flex;
    justify-content: between;
    align-items: center;
}

.accordion-header:hover {
    background: #f3f4f6;
}

.accordion-header.active {
    background: var(--primary-color);
    color: white;
}

.accordion-content {
    padding: 0 1.5rem;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease, padding 0.3s ease;
}

.accordion-content.active {
    max-height: 1000px;
    padding: 1.5rem;
}

/* Icon styles */
.section-icon {
    width: 3rem;
    height: 3rem;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: white;
}

/* Button styles */
.btn-primary {
    background: var(--primary-color);
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    border: 2px solid var(--primary-color);
}

.btn-primary:hover {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(2, 54, 121, 0.3);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    padding: 0.75rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

/* Contact section */
.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--bg-light);
    border-radius: 8px;
    transition: background-color 0.3s ease;
}

.contact-item:hover {
    background: #f3f4f6;
}

.contact-icon {
    width: 2.5rem;
    height: 2.5rem;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
}

/* Checklist styles */
.checklist-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    margin-bottom: 0.5rem;
}

.checklist-icon {
    width: 1.5rem;
    height: 1.5rem;
    background: var(--accent-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
    margin-top: 0.25rem;
}

/* Responsive design */
@media (max-width: 768px) {
    .section-title {
        font-size: 2rem;
    }
    
    .hero-section {
        min-height: 80vh;
        padding: 2rem 0;
    }
    
    .section {
        padding: 2rem 0;
    }
    
    .info-card {
        padding: 1.5rem;
    }
    
    .accordion-header {
        padding: 1rem;
    }
    
    .accordion-content.active {
        padding: 1rem;
    }
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Loading animation */
.fade-in {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}
